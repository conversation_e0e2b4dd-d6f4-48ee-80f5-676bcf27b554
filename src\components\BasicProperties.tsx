import { BasicPropertiesFormData } from "@/app/(core)/forms/types";
import { BasicPropertiesProps, FormElement } from "@/components/types";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { useGenerateFormTag } from "@/hooks/tansack-query/mutations/use-forms";
import useDebounce from "@/hooks/use-debounce";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId } from "@/lib/utils";
import { getBasicPropertiesSchema } from "@/schemas/properties/basicProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import { snakeCase } from "change-case";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";

const BasicProperties = ({ hasHint, hasTooltip, hasRequired, hasValidate, hasTag }: BasicPropertiesProps) => {
  const { isGeneratingTag, generateTag, tag, generateTagSuccess } = useGenerateFormTag();
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const elementId = selectedFormBuilderItem?.id ?? "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) ?? "";
  }

  const form = useForm<BasicPropertiesFormData>({
    resolver: zodResolver(getBasicPropertiesSchema(elementId)),
    mode: "onChange",
    defaultValues: {
      hint: selectedFormBuilderItem?.hint,
      tooltip: selectedFormBuilderItem?.tooltip,
      required: selectedFormBuilderItem?.required,
      validate: selectedFormBuilderItem?.validate,
      tag: selectedFormBuilderItem?.tag,
    },
  });

  const {
    setValue,
    trigger,
    formState: { errors },
  } = form;
  const applyChanges = (data: BasicPropertiesFormData) => {
    if (!hasHint) {
      delete data["hint"];
    }

    if (!hasTooltip) {
      delete data["tooltip"];
    }

    if (!hasTag) {
      delete data["tag"];
    }

    if (!hasRequired) {
      delete data["required"];
    }

    if (!hasValidate) {
      delete data["validate"];
    }

    const newFormElement = {
      ...selectedFormBuilderItem,
      ...data,
    } as FormElement;

    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  // Track label input changes from form elements
  const [lastLabelFromInput, setLastLabelFromInput] = useState<string>("");
  const enteredLabel = useDebounce(lastLabelFromInput);

  // Effect to generate tag when label input changes (not when item is selected)
  useEffect(() => {
    if (enteredLabel && hasTag) {
      generateTag({ label: enteredLabel });
    }
  }, [enteredLabel]);

  // Listen for label changes from form element inputs
  useEffect(() => {
    const handleLabelChange = (event: CustomEvent<{ label: string; elementId: string }>) => {
      if (event.detail.elementId === selectedFormBuilderItem?.id) {
        setLastLabelFromInput(event.detail.label);
      }
    };

    window.addEventListener("labelInputChange", handleLabelChange as EventListener);
    return () => {
      window.removeEventListener("labelInputChange", handleLabelChange as EventListener);
    };
  }, [selectedFormBuilderItem?.id]);

  // Effect to update form element when tag generation is successful
  useEffect(() => {
    if (generateTagSuccess && tag) {
      const newFormElement = {
        ...selectedFormBuilderItem,
        tag,
      } as FormElement;

      setValue("tag", tag);
      dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
    }
  }, [generateTagSuccess]);

  // Effect to update form values when selectedFormBuilderItem changes
  useEffect(() => {
    setValue("hint", selectedFormBuilderItem?.hint);
    setValue("tooltip", selectedFormBuilderItem?.tooltip);
    setValue("tag", selectedFormBuilderItem?.tag ?? "");
    setValue("required", selectedFormBuilderItem?.required || false);
    setValue("validate", selectedFormBuilderItem?.validate || false);
    trigger();
  }, [selectedFormBuilderItem, setValue, trigger]);

  const hasSeparator = (hasHint || hasTooltip || hasTag) && (hasRequired || hasValidate);

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(applyChanges)}>
        <div className="flex flex-col gap-2">
          {hasHint && (
            <FormField
              control={form.control}
              name="hint"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Hint</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Hint"
                      className={`h-[6rem] resize-none rounded-[10px] ${errors.hint && "border-destructive"}`}
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          )}
          {hasTooltip && (
            <FormField
              control={form.control}
              name="tooltip"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tooltip</FormLabel>
                  <FormControl>
                    <Input placeholder="Tooltip" {...field} className={`${errors.tooltip && "border-destructive"}`} />
                  </FormControl>
                </FormItem>
              )}
            />
          )}
          {hasTag && (
            <FormField
              control={form.control}
              name="tag"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tag</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={isGeneratingTag ? "Generating tag..." : "Tag"}
                      {...field}
                      className={`${errors.tag && "border-destructive"}`}
                      onChange={e => {
                        // Ensure whitespace is converted to underscores
                        const value = e.target.value;
                        const snake = value.includes(" ") ? value.replace(/ /g, "_").toLowerCase() : snakeCase(value);
                        field.onChange(snake);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {hasSeparator && <Separator className="bg-[#DBDFF0]" />}
          <div className="flex items-center gap-4">
            {hasRequired && (
              <FormField
                control={form.control}
                name="required"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-1">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Required</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            )}
            {hasValidate && (
              <FormField
                control={form.control}
                name="validate"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-1">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Validate</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            )}
          </div>
        </div>
      </form>
    </Form>
  );
};

export default BasicProperties;
